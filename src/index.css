@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%; /* slate-900 */

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --primary: 24 74% 58%; /* orange-500 */
    --primary-foreground: 0 0% 100%; /* white */

    --secondary: 33 100% 96%; /* orange-50 */
    --secondary-foreground: 194 100% 7%; /* orange-900 */

    --muted: 33 100% 96%; /* orange-50 */
    --muted-foreground: 33 5% 45%; /* neutral-600 */

    --accent: 33 100% 96%; /* orange-50 */
    --accent-foreground: 194 100% 7%; /* orange-900 */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 33 82% 89%; /* orange-200 */
    --input: 33 82% 89%; /* orange-200 */
    --ring: 24 74% 58%; /* orange-500 */

    --chart-1: 24 74% 58%; /* orange-500 */
    --chart-2: 45 93% 58%; /* amber-400 */
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.75rem;

    --sidebar-background: 33 100% 98%; /* orange-25 */
    --sidebar-foreground: 33 5.3% 26.1%;
    --sidebar-primary: 24 74% 58%; /* orange-500 */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 33 100% 95%; /* orange-100 */
    --sidebar-accent-foreground: 194 100% 7%; /* orange-900 */
    --sidebar-border: 33 82% 89%; /* orange-200 */
    --sidebar-ring: 24 74% 58%; /* orange-500 */
  }

  .dark {
    --background: 222.2 47.4% 11.2%; /* slate-900 */
    --foreground: 210 40% 98%; /* slate-50 */

    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 74% 58%; /* orange-500 */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%; /* slate-800 */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%; /* slate-400 */

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%; /* slate-300 */

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 24 74% 58%; /* orange-500 */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 24 74% 58%; /* orange-500 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}